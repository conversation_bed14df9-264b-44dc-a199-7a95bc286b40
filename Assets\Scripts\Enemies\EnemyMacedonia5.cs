using System.Collections.Generic;
using UnityEngine;

public class EnemyMacedonia5 : EnemyMacedonia
{
    private bool hasSlicedMelon;
    private bool isInMelonSliceMode;

    protected override void SpawnTargets()
    {
        Weapon weapon = player.GetComponentInChildren<Weapon>();
        Quaternion currentRotation = weapon.transform.rotation;

        float initialOffset = Utils.CalculateRandomOffset(bufferAngle);

        activeTargets.Clear();

        List<GameObject> targetsToSpawn = new List<GameObject>();

        // Target indices based on the prefab setup:
        // 0 - Target Raspberry
        // 1 - Target Peach
        // 2 - Target Nectarine
        // 3 - Target Melon
        // 4 - Target Melon Slice

        const int melonIndex = 3;
        const int melonSliceIndex = 4;

        // If we're in melon slice mode (after slicing at least one melon), spawn only 4 melon slices
        if (isInMelonSliceMode)
        {
            numberOfTargets = 4;
            for (int i = 0; i < numberOfTargets; i++)
            {
                targetsToSpawn.Add(targetPrefabs[melonSliceIndex]);
            }
            isInMelonSliceMode = false; // Reset after this round
        }
        else
        {
            // Normal spawning logic with restrictions
            int melonCount = 0;

            for (int i = 0; i < numberOfTargets; i++)
            {
                int selectedIndex;
                int attempts = 0;

                do
                {
                    selectedIndex = Random.Range(0, targetPrefabs.Count - 1); // Exclude melon slice from random selection

                    // Cannot spawn more than 2 melons in the same loop
                    if (selectedIndex == melonIndex && melonCount >= 2)
                    {
                        continue;
                    }

                    break;
                } while (++attempts < 50);

                if (selectedIndex == melonIndex)
                {
                    melonCount++;
                }

                targetsToSpawn.Add(targetPrefabs[selectedIndex]);
            }
        }

        // Spawn the targets
        for (int i = 0; i < targetsToSpawn.Count; i++)
        {
            GameObject targetObject = Instantiate(targetsToSpawn[i], transform);
            targetObject.transform.position = spawnPoint.transform.position;

            Target target = targetObject.GetComponent<Target>();
            if (target != null)
            {
                // Apply position and rotation
                if (i == 0)
                {
                    target.RotateTo(currentRotation, initialOffset);
                }
                else
                {
                    int sign = initialOffset < 0 ? -1 : 1;
                    target.RotateTo(currentRotation, sign * offsetAngle);
                }

                currentRotation = target.transform.rotation;

                // Apply custom behaviors based on target type
                ApplyCustomTargetBehavior(targetObject, targetsToSpawn[i]);

                activeTargets.Add(targetObject);
            }
        }
    }

    private void ApplyCustomTargetBehavior(GameObject targetObject, GameObject targetPrefab)
    {
        const int raspberryIndex = 0;
        const int nectarineIndex = 2;

        // Check which target type this is by comparing with prefabs
        for (int i = 0; i < targetPrefabs.Count; i++)
        {
            if (targetPrefabs[i] == targetPrefab)
            {
                if (i == raspberryIndex)
                {
                    // Raspberry: random scale between 0.2 and 1 (scale fruit and collider)
                    float randomScale = Random.Range(0.2f, 1.0f);

                    // Scale the fruit visual
                    Transform fruit = targetObject.transform.Find("Fruit");
                    if (fruit != null)
                    {
                        fruit.localScale = Vector3.one * randomScale;
                    }

                    // Scale the polygon collider to match
                    PolygonCollider2D polygonCollider = targetObject.GetComponent<PolygonCollider2D>();
                    if (polygonCollider != null)
                    {
                        // Store original points if not already stored
                        if (polygonCollider.points.Length > 0)
                        {
                            Vector2[] originalPoints = polygonCollider.points;
                            Vector2[] scaledPoints = new Vector2[originalPoints.Length];

                            for (int j = 0; j < originalPoints.Length; j++)
                            {
                                scaledPoints[j] = originalPoints[j] * randomScale;
                            }

                            polygonCollider.points = scaledPoints;
                        }
                    }
                }
                else if (i == nectarineIndex)
                {
                    // Nectarine: 1 in 3 chance for vampire mode
                    if (Random.Range(0, 3) == 0) // 1 in 3 chance
                    {
                        TargetNectarine nectarineTarget = targetObject.GetComponent<TargetNectarine>();
                        if (nectarineTarget != null)
                        {
                            nectarineTarget.SetVampireStats();
                        }
                    }
                }
                break;
            }
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        Target.OnTargetDestroyed += CheckForMelonSlice;
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        Target.OnTargetDestroyed -= CheckForMelonSlice;
    }

    private void CheckForMelonSlice(GameObject destroyedTarget)
    {
        // Check if the destroyed target was a melon
        if (destroyedTarget != null)
        {
            Target target = destroyedTarget.GetComponent<Target>();
            if (target != null && !target.isRotten)
            {
                // Check if this was a melon by looking at the target type
                if (destroyedTarget.GetComponent<TargetMelon>() != null)
                {
                    hasSlicedMelon = true;
                    isInMelonSliceMode = true;
                }
            }
        }
    }
}
